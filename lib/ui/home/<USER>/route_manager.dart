import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>/marker_utils.dart';

class RouteManager {
  /// Create a polyline for return order route
  static Polyline createReturnOrderPolyline(List<LatLng> routePoints) {
    return Polyline(
      polylineId: const PolylineId("rota_de_retorno"),
      points: routePoints,
      color: Colors.red,
      width: 5,
    );
  }

  /// Create a polyline for regular order route
  static Polyline createOrderPolyline(String routeId, List<LatLng> routePoints) {
    return Polyline(
      polylineId: PolylineId(routeId),
      points: routePoints,
      color: Colors.orange,
      width: 5,
    );
  }

  /// Create a destination marker for return orders
  static Marker createReturnDestinationMarker(
    LatLng position,
    OrderModel returnOrder,
    VoidCallback onTap,
  ) {
    return Marker(
      markerId: const MarkerId('destino'),
      position: position,
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
      infoWindow: InfoWindow(
        title: "Devolução para: ${returnOrder.vendor.title}",
        snippet: "",
      ),
      onTap: onTap,
    );
  }

  /// Create a destination marker for regular orders
  static Marker createDestinationMarker(
    LatLng position,
    String title,
    String snippet,
    bool isDeliveryInProgress,
    VoidCallback onTap,
  ) {
    return Marker(
      markerId: const MarkerId('destino'),
      position: position,
      icon: BitmapDescriptor.defaultMarkerWithHue(
        isDeliveryInProgress ? BitmapDescriptor.hueRed : BitmapDescriptor.hueGreen,
      ),
      infoWindow: InfoWindow(title: title, snippet: snippet),
      onTap: onTap,
    );
  }

  /// Create a store marker for available orders
  static Marker createStoreMarker(
    String markerId,
    LatLng position,
    String title,
    int orderCount,
    VoidCallback onTap,
  ) {
    return Marker(
      markerId: MarkerId(markerId),
      position: position,
      icon: BitmapDescriptor.defaultMarkerWithHue(
        orderCount > 1 ? BitmapDescriptor.hueOrange : BitmapDescriptor.hueGreen,
      ),
      infoWindow: InfoWindow(title: title, snippet: ""),
      onTap: onTap,
    );
  }

  /// Get route coordinates and create polyline
  static Future<Polyline?> createRoutePolyline(
    LatLng start,
    LatLng end,
    String routeId, {
    Color color = Colors.orange,
    int width = 5,
  }) async {
    try {
      final routePoints = await getRouteCoordinates(start, end);
      return Polyline(
        polylineId: PolylineId(routeId),
        points: routePoints,
        color: color,
        width: width,
      );
    } catch (e) {
      log("Error creating route polyline: $e");
      return null;
    }
  }

  /// Get destination position based on order status
  static LatLng? getDestinationPosition(OrderModel order, bool isDeliveryInProgress) {
    if (isDeliveryInProgress) {
      // Get client address
      return MarkerUtils.getLatLng(
        order.author.shippingAddress
            ?.firstWhere(
              (a) => a.isDefault == true,
              orElse: () => order.author.shippingAddress!.first,
            )
            .location
            ?.geoPoint,
      );
    } else {
      // Get store address
      return MarkerUtils.getLatLng(order.vendor.address_store?.location.geoPoint);
    }
  }

  /// Get destination info based on order status
  static Map<String, String> getDestinationInfo(bool isDeliveryInProgress) {
    if (isDeliveryInProgress) {
      return {
        'title': 'Entrega',
        'snippet': '',
        'routeId': 'rota_ate_cliente',
      };
    } else {
      return {
        'title': 'Loja',
        'snippet': '',
        'routeId': 'rota_ate_loja',
      };
    }
  }
}

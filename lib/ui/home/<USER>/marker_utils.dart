import 'dart:ui' as ui;
import 'dart:typed_data';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class MarkerUtils {
  /// Convert GeoPoint to LatLng
  static LatLng? getLatLng(GeoPoint? location) {
    if (location == null) return null;
    return LatLng(location.latitude, location.longitude);
  }

  /// Load custom marker icon from assets
  static Future<BitmapDescriptor?> loadCustomMarkerIcon() async {
    try {
      final Uint8List markerIcon =
          await getBytesFromAsset('assets/images/motoentregador.png', 40);
      return BitmapDescriptor.bytes(markerIcon);
    } catch (e) {
      // Return default marker if custom icon fails to load
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);
    }
  }

  /// Get bytes from asset with specified width
  static Future<Uint8List> getBytesFromAsset(String path, int width) async {
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: width);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }

  /// Create current position marker
  static Marker createCurrentPositionMarker(
    LatLng position,
    BitmapDescriptor? icon,
    double heading,
  ) {
    return Marker(
      markerId: const MarkerId('current_position'),
      position: position,
      icon: icon ?? BitmapDescriptor.defaultMarker,
      infoWindow: const InfoWindow(title: "Minha Localização"),
      zIndex: 2,
      rotation: heading,
      anchor: const Offset(0.5, 0.5),
    );
  }

  /// Create store marker
  static Marker createStoreMarker(
    String lojaId,
    LatLng position,
    String title,
    int orderCount,
    VoidCallback onTap,
  ) {
    return Marker(
      markerId: MarkerId('loja_$lojaId'),
      position: position,
      icon: BitmapDescriptor.defaultMarkerWithHue(orderCount > 1
          ? BitmapDescriptor.hueOrange
          : BitmapDescriptor.hueGreen),
      infoWindow: InfoWindow(title: title, snippet: ""),
      onTap: onTap,
    );
  }

  /// Create destination marker
  static Marker createDestinationMarker(
    LatLng position,
    String title,
    String snippet,
    bool isDeliveryInProgress,
    VoidCallback onTap,
  ) {
    return Marker(
      markerId: const MarkerId('destino'),
      position: position,
      icon: BitmapDescriptor.defaultMarkerWithHue(isDeliveryInProgress
          ? BitmapDescriptor.hueRed
          : BitmapDescriptor.hueGreen),
      infoWindow: InfoWindow(title: title, snippet: snippet),
      onTap: onTap,
    );
  }
}

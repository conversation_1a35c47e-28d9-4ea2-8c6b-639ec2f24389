import 'dart:async';
import 'dart:developer';

import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class LocationManager {
  StreamSubscription<Position>? _positionStreamSubscription;
  LatLng? _currentPosition;
  double _currentHeading = 0.0;

  // Callbacks
  Function(LatLng)? onPositionChanged;
  Function(double)? onHeadingChanged;

  LatLng? get currentPosition => _currentPosition;
  double get currentHeading => _currentHeading;

  /// Initialize location manager with callbacks
  void initialize({
    Function(LatLng)? onPositionChanged,
    Function(double)? onHeadingChanged,
  }) {
    this.onPositionChanged = onPositionChanged;
    this.onHeadingChanged = onHeadingChanged;
  }

  /// Determine current position
  Future<LatLng?> determinePosition() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        log("Location services not enabled");
        return null;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.deniedForever) {
          log("Location permission denied forever");
          return null;
        }
      }

      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
        ),
      );

      _currentPosition = LatLng(position.latitude, position.longitude);
      log("Current position determined: ${position.latitude}, ${position.longitude}");

      return _currentPosition;
    } catch (e) {
      log("Error determining position: $e");
      return null;
    }
  }

  /// Start position updates
  void startPositionUpdates() {
    _positionStreamSubscription?.cancel();

    const locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10,
    );

    _positionStreamSubscription =
        Geolocator.getPositionStream(locationSettings: locationSettings)
            .listen((Position position) {
      _currentPosition = LatLng(position.latitude, position.longitude);
      _currentHeading = position.heading;

      onPositionChanged?.call(_currentPosition!);
      onHeadingChanged?.call(_currentHeading);
    });
  }

  /// Stop position updates
  void stopPositionUpdates() {
    _positionStreamSubscription?.cancel();
    _positionStreamSubscription = null;
  }

  /// Dispose resources
  void dispose() {
    stopPositionUpdates();
  }
}

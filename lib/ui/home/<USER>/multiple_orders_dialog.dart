import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/ui/home/<USER>/distance_calculator.dart';
import 'package:emartdriver/ui/home/<USER>/marker_utils.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class MultipleOrdersDialog extends StatelessWidget {
  final List<OrderModel> orders;
  final LatLng? currentPosition;
  final Function(OrderModel) onOrderSelected;

  const MultipleOrdersDialog({
    super.key,
    required this.orders,
    required this.currentPosition,
    required this.onOrderSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.70,
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(height: 20),
              GestureDetector(
                onTap: () {},
                child: Container(
                  width: 50,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              )
            ],
          ),
          const SizedBox(height: 20),
          Text(
            'Loja: ${orders.first.vendor.title}',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xff425799),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 10),
          Container(
            padding: const EdgeInsets.all(10),
            decoration: const BoxDecoration(
              color: Color.fromARGB(39, 201, 212, 247),
            ),
            width: MediaQuery.of(context).size.width,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset('assets/images/caixa.png', height: 30, width: 30),
                const SizedBox(width: 8),
                Text(
                  "${orders.length} pedidos disponíveis",
                  style: const TextStyle(
                      fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          const SizedBox(height: 15),
          Expanded(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: orders.length,
              itemBuilder: (context, index) {
                final order = orders[index];
                LatLng? storePos = MarkerUtils.getLatLng(
                    order.vendor.address_store?.location.geoPoint);
                double storeDistance = 0;

                if (currentPosition != null && storePos != null) {
                  storeDistance = DistanceCalculator.calculateDistance(
                      currentPosition!.latitude,
                      currentPosition!.longitude,
                      storePos.latitude,
                      storePos.longitude);
                }

                return Card(
                  margin:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                  elevation: 3,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                    side: const BorderSide(color: Color(0xff425799), width: 1),
                  ),
                  child: InkWell(
                    borderRadius: BorderRadius.circular(12),
                    onTap: () {
                      Navigator.pop(context);
                      onOrderSelected(order);
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  "Pedido #${order.id.substring(0, 8)}",
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Color(0xff425799),
                                  ),
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Colors.green.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  "R\$ ${order.price ?? '0,00'}",
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.green,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            "Cliente: ${order.author.firstName} ${order.author.lastName}",
                            style: const TextStyle(fontSize: 14),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            "Distância da loja: ${storeDistance.toStringAsFixed(1)} km",
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              const Icon(Icons.location_on,
                                  size: 16, color: Colors.orange),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  order.author.shippingAddress
                                          ?.firstWhere(
                                              (a) => a.isDefault == true,
                                              orElse: () => order.author
                                                  .shippingAddress!.first)
                                          .getFullAddress() ??
                                      "Endereço não disponível",
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

/// Show multiple orders dialog
void showMultipleOrdersDialog(
  BuildContext context,
  List<OrderModel> orders,
  LatLng? currentPosition,
  Function(OrderModel) onOrderSelected,
) {
  showModalBottomSheet(
    enableDrag: true,
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.white,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    builder: (BuildContext context) {
      return MultipleOrdersDialog(
        orders: orders,
        currentPosition: currentPosition,
        onOrderSelected: onOrderSelected,
      );
    },
  );
}

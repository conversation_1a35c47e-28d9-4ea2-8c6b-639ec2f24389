import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';

class MapStateManager {
  GoogleMapController? _mapController;
  final Map<String, Marker> _markers = {};
  final Set<Polyline> _polylines = {};
  LatLng? _currentPosition;
  BitmapDescriptor? _deliveryPersonIcon;
  double _currentHeading = 0.0;

  // Getters
  GoogleMapController? get mapController => _mapController;
  Map<String, Marker> get markers => _markers;
  Set<Polyline> get polylines => _polylines;
  LatLng? get currentPosition => _currentPosition;
  BitmapDescriptor? get deliveryPersonIcon => _deliveryPersonIcon;
  double get currentHeading => _currentHeading;

  // Setters
  set mapController(GoogleMapController? controller) => _mapController = controller;
  set currentPosition(LatLng? position) => _currentPosition = position;
  set deliveryPersonIcon(BitmapDescriptor? icon) => _deliveryPersonIcon = icon;
  set currentHeading(double heading) => _currentHeading = heading;

  /// Initialize map controller
  void initializeMapController(GoogleMapController controller) {
    _mapController = controller;
    log("Map controller initialized");
  }

  /// Update current position
  void updateCurrentPosition(Position position) {
    _currentPosition = LatLng(position.latitude, position.longitude);
    _currentHeading = position.heading;
    log("Position updated: ${position.latitude}, ${position.longitude}");
  }

  /// Add marker to map
  void addMarker(String key, Marker marker) {
    _markers[key] = marker;
  }

  /// Remove marker from map
  void removeMarker(String key) {
    _markers.remove(key);
  }

  /// Clear all markers except current position
  void clearMarkersExceptCurrentPosition() {
    final currentPosMarker = _markers['current_position'];
    _markers.clear();
    if (currentPosMarker != null) {
      _markers['current_position'] = currentPosMarker;
    }
  }

  /// Clear all markers
  void clearAllMarkers() {
    _markers.clear();
  }

  /// Add polyline to map
  void addPolyline(Polyline polyline) {
    _polylines.add(polyline);
  }

  /// Remove polyline from map
  void removePolyline(String polylineId) {
    _polylines.removeWhere((polyline) => polyline.polylineId.value == polylineId);
  }

  /// Clear all polylines
  void clearPolylines() {
    _polylines.clear();
  }

  /// Update current position marker
  void updateCurrentPositionMarker() {
    if (_currentPosition != null) {
      _markers['current_position'] = Marker(
        markerId: const MarkerId('current_position'),
        position: _currentPosition!,
        icon: _deliveryPersonIcon ?? BitmapDescriptor.defaultMarker,
        infoWindow: const InfoWindow(title: "Minha Localização"),
        zIndex: 2,
        rotation: _currentHeading,
      );
    }
  }

  /// Center map on current location
  void centerOnCurrentLocation() {
    if (_currentPosition == null || _mapController == null) {
      log("Cannot center map: position or controller is null");
      return;
    }

    try {
      _mapController!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: _currentPosition!,
            zoom: 17.0,
            tilt: 0,
          ),
        ),
      );
      log("Map centered on current location");
    } catch (e) {
      log("Error centering map: $e");
    }
  }

  /// Zoom in
  void zoomIn() {
    if (_mapController == null) return;

    try {
      _mapController!.getZoomLevel().then((currentZoom) {
        double newZoom = currentZoom + 1.0;
        newZoom = newZoom > 20.0 ? 20.0 : newZoom;

        _mapController!.animateCamera(
          CameraUpdate.zoomTo(newZoom),
        );
      });
    } catch (e) {
      log("Error zooming in: $e");
    }
  }

  /// Zoom out
  void zoomOut() {
    if (_mapController == null) return;

    try {
      _mapController!.getZoomLevel().then((currentZoom) {
        double newZoom = currentZoom - 1.0;
        newZoom = newZoom < 2.0 ? 2.0 : newZoom;

        _mapController!.animateCamera(
          CameraUpdate.zoomTo(newZoom),
        );
      });
    } catch (e) {
      log("Error zooming out: $e");
    }
  }

  /// Animate camera to position
  void animateCameraToPosition(LatLng position, {double zoom = 15.0}) {
    if (_mapController == null) return;

    try {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(position, zoom),
      );
    } catch (e) {
      log("Error animating camera: $e");
    }
  }

  /// Force map reload
  void forceMapReload() {
    log("Forcing map reload");
    _markers.clear();
    _polylines.clear();
  }

  /// Dispose resources
  void dispose() {
    try {
      _mapController?.dispose();
    } catch (e) {
      log("Error disposing map controller: $e");
    }
    _markers.clear();
    _polylines.clear();
  }
}

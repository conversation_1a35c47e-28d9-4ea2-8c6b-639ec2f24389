import 'dart:developer';

import 'package:google_maps_flutter/google_maps_flutter.dart';

class MapManager {
  GoogleMapController? _mapController;
  final Map<String, Marker> _markers = {};
  final Set<Polyline> _polylines = {};

  GoogleMapController? get mapController => _mapController;
  Map<String, Marker> get markers => _markers;
  Set<Polyline> get polylines => _polylines;

  /// Initialize map controller
  void onMapCreated(GoogleMapController controller, LatLng? currentPosition) {
    try {
      _mapController = controller;
      if (currentPosition != null) {
        controller.animateCamera(
          CameraUpdate.newLatLngZoom(currentPosition, 15),
        );
      } else {
        log("Current position is null in onMapCreated");
      }
      log("Map controller created successfully");
    } catch (e) {
      log("Error in onMapCreated: $e");
    }
  }

  /// Center map on current location
  void centerOnCurrentLocation(LatLng? currentPosition) {
    if (currentPosition == null || _mapController == null) {
      log("Cannot center map: position or controller is null");
      return;
    }

    try {
      _mapController!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: currentPosition,
            zoom: 17.0,
            tilt: 0,
          ),
        ),
      );
    } catch (e) {
      log("Error centering map: $e");
    }
  }

  /// Zoom in
  void zoomIn() {
    if (_mapController == null) return;

    try {
      _mapController!.getZoomLevel().then((currentZoom) {
        double newZoom = currentZoom + 1.0;
        newZoom = newZoom > 20.0 ? 20.0 : newZoom;

        _mapController!.animateCamera(
          CameraUpdate.zoomTo(newZoom),
        );
      });
    } catch (e) {
      log("Error zooming in: $e");
    }
  }

  /// Zoom out
  void zoomOut() {
    if (_mapController == null) return;

    try {
      _mapController!.getZoomLevel().then((currentZoom) {
        double newZoom = currentZoom - 1.0;
        newZoom = newZoom < 2.0 ? 2.0 : newZoom;

        _mapController!.animateCamera(
          CameraUpdate.zoomTo(newZoom),
        );
      });
    } catch (e) {
      log("Error zooming out: $e");
    }
  }

  /// Add marker
  void addMarker(String key, Marker marker) {
    _markers[key] = marker;
  }

  /// Remove marker
  void removeMarker(String key) {
    _markers.remove(key);
  }

  /// Clear all markers except current position
  void clearMarkersExceptCurrentPosition() {
    final currentPosMarker = _markers['current_position'];
    _markers.clear();
    if (currentPosMarker != null) {
      _markers['current_position'] = currentPosMarker;
    }
  }

  /// Clear all markers
  void clearAllMarkers() {
    _markers.clear();
  }

  /// Add polyline
  void addPolyline(Polyline polyline) {
    _polylines.add(polyline);
  }

  /// Clear polylines
  void clearPolylines() {
    _polylines.clear();
  }

  /// Force map reload
  void forceMapReload() {
    log("Forcing map reload");
    _markers.clear();
    _polylines.clear();
  }

  /// Dispose resources
  void dispose() {
    try {
      _mapController?.dispose();
    } catch (e) {
      log("Error disposing map controller: $e");
    }
  }
}

import 'dart:async';
import 'dart:developer';
import 'dart:math' as math;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/main.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/services/OrderMonitor.dart';
import 'package:emartdriver/ui/home/<USER>/return_order_bottom_sheet.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>/distance_calculator.dart';
import 'package:emartdriver/ui/home/<USER>/marker_utils.dart';
import 'package:emartdriver/vendor_status_enum.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class HomeScreen extends StatefulWidget {
  final bool isOnline;

  const HomeScreen({super.key, required this.isOnline});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  late GoogleMapController _mapController;
  final Map<String, Marker> _markers = {};
  final Set<Polyline> _polylines = {};

  LatLng? _currentPosition;
  bool _isLoading = true;

  OrderModel? _selectedOrder;
  RouteInfo? _selectedRouteInfo;
  bool _pedidoAceito = false;

  StreamSubscription<QuerySnapshot>? _ordersSubscription;
  StreamSubscription<DocumentSnapshot>? _acceptedOrderSubscription;
  StreamSubscription<Position>? _positionStreamSubscription;

  BitmapDescriptor? _deliveryPersonIcon;

  final String CURRENT_USER_ID = FirebaseAuth.instance.currentUser?.uid ?? '';

  final OrderMonitor _orderMonitor = OrderMonitor();

  OrderModel? _newOrder;
  bool _showingNewOrderNotification = false;

  @override
  void initState() {
    super.initState();

    _loadCustomMarkerIcon().then((_) {
      _determinePosition();
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      globalContext = context;
    });

    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didUpdateWidget(HomeScreen oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if online status changed
    if (oldWidget.isOnline != widget.isOnline) {
      _handleOnlineStatusChange();
    }
  }

  void _handleOnlineStatusChange() {
    log("Online status changed to: ${widget.isOnline}");

    if (widget.isOnline) {
      // User went online - start listening to orders
      if (_currentPosition != null) {
        _initializeOrderMonitor();
        _startFirebaseListeners();
      }
    } else {
      // User went offline - stop listening and clear orders
      _orderMonitor.dispose();
      _ordersSubscription?.cancel();
      _acceptedOrderSubscription?.cancel();

      setState(() {
        if (!_pedidoAceito) {
          _clearMarkersExceptCurrentPosition();
        }
      });
    }
  }

  void _initializeOrderMonitor() {
    if (_currentPosition != null && widget.isOnline) {
      _orderMonitor.initialize(
        onNewOrder: _handleNewOrder,
        currentPosition: _currentPosition,
        currentUserId: CURRENT_USER_ID,
      );
    }
  }

  void _handleNewOrder(OrderModel order) {
    if (order.status == OrderStatus.driverAccepted.description &&
        order.entregador_id != null &&
        order.entregador_id!.isNotEmpty &&
        order.entregador_id != CURRENT_USER_ID) {
      log("Pedido ${order.id} já foi aceito por outro entregador: ${order.entregador_id}");

      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
        content: Text("Este pedido já foi aceito por outro entregador"),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 3),
      ));

      return;
    }

    if (!_pedidoAceito && !_showingNewOrderNotification) {
      setState(() {
        _newOrder = order;
        _showingNewOrderNotification = true;
      });

      LatLng? storePos =
          MarkerUtils.getLatLng(order.vendor.address_store?.location.geoPoint);
      double distanceInKm = 0.0;

      if (_currentPosition != null && storePos != null) {
        distanceInKm = DistanceCalculator.calculateDistance(
            _currentPosition!.latitude,
            _currentPosition!.longitude,
            storePos.latitude,
            storePos.longitude);
      }

      showOrderBottomSheet(
        context: context,
        order: order,
        distanceInKm: distanceInKm,
        durationInMinutes: 0.0,
        onAccept: () => _acceptNewOrder(order),
        onDecline: () => setState(() {
          _newOrder = null;
          _showingNewOrderNotification = false;
        }),
        pedidoAceito: _pedidoAceito,
      );
    }
  }

  void _acceptNewOrder(OrderModel order) async {
    try {
      final docRef =
          FirebaseFirestore.instance.collection(ORDERS).doc(order.id);

      await docRef.update({
        "entregador_id": CURRENT_USER_ID,
        "horaAceite": Timestamp.now(),
        "status": OrderStatus.driverAccepted.description,
      });

      // try {
      //   final apiResult = await TaEntregueApiService.acceptOrder(
      //     idRemoto: order.id,
      //     idCliente: int.tryParse(order.authorID) ?? 0,
      //     idEntregador: int.tryParse(CURRENT_USER_ID) ?? 0,
      //     valor: order.price ?? "0,00",
      //   );

      //   if (!apiResult['success']) {
      //     log("Aviso: API externa retornou erro: ${apiResult['mensagem']}");
      //   } else {
      //     log("Pedido aceito na API externa com sucesso");
      //   }
      // } catch (apiError) {
      //   log("Erro ao chamar API externa: $apiError");
      // }

      final updatedDoc = await docRef.get();
      final updatedOrder = OrderModel.fromJson(updatedDoc.data()!);

      setState(() {
        _selectedOrder = updatedOrder;
        _pedidoAceito = true;
        _newOrder = null;
      });

      _listenToAcceptedOrder(_selectedOrder!.id);

      Future.delayed(const Duration(seconds: 1), () {
        presentOrderAcceptedDialog(context, updatedOrder);

        Future.delayed(const Duration(milliseconds: 500), () {
          showStoreInfoDialog(context, updatedOrder);
        });
      });
    } catch (e) {
      log("Erro ao aceitar pedido: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Erro ao aceitar o pedido.")),
      );
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      log("App resumed, reloading map");

      _forceMapReload();
    }
  }

  Future<void> _loadCustomMarkerIcon() async {
    try {
      _deliveryPersonIcon = await MarkerUtils.loadCustomMarkerIcon();
      setState(() {});
    } catch (e) {
      log("Error in _loadCustomMarkerIcon: $e");
      _deliveryPersonIcon =
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);
    }
  }

  @override
  void dispose() {
    _ordersSubscription?.cancel();
    _acceptedOrderSubscription?.cancel();
    _positionStreamSubscription?.cancel();
    _orderMonitor.dispose();
    WidgetsBinding.instance.removeObserver(this);

    try {
      if (mounted) {
        _mapController.dispose();
      }
    } catch (e) {
      log("Error disposing map controller: $e");
    }
    super.dispose();
  }

  Future<void> _determinePosition() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        log("Location services not enabled");
        setState(() => _isLoading = false);
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.deniedForever) {
          log("Location permission denied forever");
          setState(() => _isLoading = false);
          return;
        }
      }

      final position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high);

      setState(() {
        _currentPosition = LatLng(position.latitude, position.longitude);
        _isLoading = false;

        _updateCurrentPositionMarker();
      });

      log("Current position determined: ${position.latitude}, ${position.longitude}");

      _startPositionUpdates();
      _startFirebaseListeners();

      _initializeOrderMonitor();

      if (mounted && _currentPosition != null) {
        _mapController.animateCamera(
          CameraUpdate.newLatLngZoom(_currentPosition!, 15),
        );
      }
    } catch (e) {
      log("Error determining position: $e");
      setState(() => _isLoading = false);
    }
  }

  void _startFirebaseListeners() {
    _ordersSubscription?.cancel();
    _acceptedOrderSubscription?.cancel();

    _checkForAcceptedOrder();
  }

  void _checkForAcceptedOrder() async {
    try {
      final returnQuery = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: CURRENT_USER_ID)
          .where('status', isEqualTo: OrderStatus.delivered.description)
          .where('has_return', isEqualTo: true)
          .limit(1);

      final returnSnapshot = await returnQuery.get();

      log("Verificando pedidos de devolução: ${returnSnapshot.docs.length} encontrados");

      if (returnSnapshot.docs.isNotEmpty) {
        final orderId = returnSnapshot.docs.first.id;
        log("Pedido de devolução encontrado com ID: $orderId");
        _listenToReturnOrder(orderId);
        return;
      }

      final query = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: CURRENT_USER_ID)
          .where('status', whereIn: [
        OrderStatus.driverAccepted.description,
        OrderStatus.driverOnTheWay.description,
        OrderStatus.driverPending.description
      ]).limit(1);

      final snapshot = await query.get();

      if (snapshot.docs.isNotEmpty) {
        final orderId = snapshot.docs.first.id;
        _listenToAcceptedOrder(orderId);
      } else {
        _listenToAvailableOrders();
      }
    } catch (e) {
      log("Error checking for accepted orders: $e");
    }
  }

  void _listenToReturnOrder(String orderId) async {
    _ordersSubscription?.cancel();

    try {
      final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(orderId);

      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        _handleReturnOrderUpdate(docSnapshot);

        _acceptedOrderSubscription = docRef.snapshots().listen((docSnapshot) {
          if (docSnapshot.exists) {
            _handleReturnOrderUpdate(docSnapshot);
          } else {
            _listenToAvailableOrders();
          }
        }, onError: (e) {
          log("Error listening to return order: $e");
        });
      } else {
        _listenToAvailableOrders();
      }
    } catch (e) {
      log("Error fetching initial return order: $e");
      _listenToAvailableOrders();
    }
  }

  void _handleReturnOrderUpdate(DocumentSnapshot docSnapshot) async {
    try {
      log("Processando pedido de devolução...");
      final returnOrder =
          OrderModel.fromJson(docSnapshot.data() as Map<String, dynamic>);

      log("Pedido de devolução: ID=${returnOrder.id}, status=${returnOrder.status}, has_return=${returnOrder.has_return}");

      if (returnOrder.status != OrderStatus.delivered ||
          !returnOrder.has_return) {
        log("Pedido não está mais no status 'delivered' ou não tem has_return=true");
        _checkForAcceptedOrder();
        return;
      }

      LatLng? storePos = MarkerUtils.getLatLng(
          returnOrder.vendor.address_store?.location.geoPoint);

      if (_currentPosition != null && storePos != null) {
        final returnRoute =
            await getRouteCoordinates(_currentPosition!, storePos);

        setState(() {
          _selectedOrder = returnOrder;
          _pedidoAceito = true;
          _polylines.clear();
          _clearMarkersExceptCurrentPosition();

          _polylines.add(Polyline(
            polylineId: const PolylineId("rota_de_retorno"),
            points: returnRoute,
            color: Colors.red,
            width: 5,
          ));

          _markers['destino'] = Marker(
            markerId: const MarkerId('destino'),
            position: storePos,
            icon: BitmapDescriptor.defaultMarkerWithHue(
                BitmapDescriptor.hueGreen),
            infoWindow: InfoWindow(
                title: "Devolução para: ${returnOrder.vendor.title}",
                snippet: ""),
            onTap: () {
              if (_pedidoAceito && _selectedOrder != null) {
                showReturnOrderBottomSheet(context, returnOrder, () {
                  setState(() {
                    _selectedOrder = null;
                    _pedidoAceito = false;
                  });
                  _checkForAcceptedOrder();
                });
              }
            },
          );
        });
      }
    } catch (e) {
      log("Error handling return order update: $e");
    }
  }

  void _listenToAcceptedOrder(String orderId) async {
    _ordersSubscription?.cancel();

    try {
      final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(orderId);

      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        _handleAcceptedOrderUpdate(docSnapshot);

        _acceptedOrderSubscription = docRef.snapshots().listen((docSnapshot) {
          if (docSnapshot.exists) {
            _handleAcceptedOrderUpdate(docSnapshot);
          } else {
            _listenToAvailableOrders();
          }
        }, onError: (e) {
          log("Error listening to accepted order: $e");
        });
      } else {
        _listenToAvailableOrders();
      }
    } catch (e) {
      log("Error fetching initial accepted order: $e");
      _listenToAvailableOrders();
    }
  }

  void _handleAcceptedOrderUpdate(DocumentSnapshot docSnapshot) async {
    try {
      final existingOrder =
          OrderModel.fromJson(docSnapshot.data() as Map<String, dynamic>);

      bool isDeliveryInProgress =
          existingOrder.status == OrderStatus.driverOnTheWay;

      bool statusChangedToOnTheWay = _selectedOrder != null &&
          _selectedOrder!.status != OrderStatus.driverOnTheWay.description &&
          existingOrder.status == OrderStatus.driverOnTheWay.description;

      LatLng? destinationPos;
      String destinationTitle;
      String destinationSnippet;
      String routeId;

      if (isDeliveryInProgress) {
        destinationPos = _getLatLng(existingOrder.author.shippingAddress
            ?.firstWhere((a) => a.isDefault == true,
                orElse: () => existingOrder.author.shippingAddress!.first)
            .location
            ?.geoPoint);
        destinationTitle = "Entrega";
        destinationSnippet = "";
        routeId = "rota_ate_cliente";
      } else {
        destinationPos =
            _getLatLng(existingOrder.vendor.address_store?.location.geoPoint);
        destinationTitle = "Loja";
        destinationSnippet = "";
        routeId = "rota_ate_loja";
      }

      if (_currentPosition != null && destinationPos != null) {
        final rota =
            await getRouteCoordinates(_currentPosition!, destinationPos);

        setState(() {
          _selectedOrder = existingOrder;
          _pedidoAceito = true;
          _polylines.clear();
          _clearMarkersExceptCurrentPosition();

          _polylines.add(Polyline(
            polylineId: PolylineId(routeId),
            points: rota,
            color: Colors.orange,
            width: 5,
          ));

          _markers['destino'] = Marker(
            markerId: const MarkerId('destino'),
            position: destinationPos ?? const LatLng(0, 0),
            icon: BitmapDescriptor.defaultMarkerWithHue(isDeliveryInProgress
                ? BitmapDescriptor.hueRed
                : BitmapDescriptor.hueGreen),
            infoWindow: InfoWindow(
                title: destinationTitle, snippet: destinationSnippet),
            onTap: () {
              if (_pedidoAceito && _selectedOrder != null) {
                showStoreInfoDialog(context, _selectedOrder!);
              } else {
                _showBottomSheet();
              }
            },
          );
        });

        if (statusChangedToOnTheWay) {
          log("Pedido coletado! Mostrando informações do cliente");

          final loja =
              _getLatLng(existingOrder.vendor.address_store?.location.geoPoint);
          final cliente = destinationPos;

          if (loja != null) {
            final info1 = await getRouteInfo(loja, cliente);

            setState(() {
              _selectedRouteInfo = RouteInfo(
                route: [],
                distance: info1.distance,
                duration: info1.duration,
              );
            });

            Future.delayed(const Duration(milliseconds: 500), () {
              if (mounted) {
                _showClientBottomSheet(existingOrder);
              }
            });
          }
        }
      }
    } catch (e) {
      log("Error handling accepted order update: $e");
    }
  }

  void _showClientBottomSheet(OrderModel order) {
    if (_selectedRouteInfo == null) return;

    showOrderBottomSheet(
      context: context,
      pedidoAceito: true,
      order: order,
      distanceInKm: (_selectedRouteInfo!.distance) / 1000,
      durationInMinutes: (_selectedRouteInfo!.duration) / 60,
      onAccept: () {},
      onDecline: () {},
    );
  }

  void _listenToAvailableOrders() async {
    _acceptedOrderSubscription?.cancel();

    // Only listen to orders if user is online
    if (!widget.isOnline) {
      log("User is offline, not listening to available orders");
      // Clear any existing orders from the map
      setState(() {
        if (!_pedidoAceito) {
          _clearMarkersExceptCurrentPosition();
        }
      });
      return;
    }

    try {
      final query = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('status', isEqualTo: OrderStatus.driverSearching.description);

      final snapshot = await query.get();

      _handleAvailableOrdersUpdate(snapshot);

      _ordersSubscription = query.snapshots().listen((querySnapshot) {
        _handleAvailableOrdersUpdate(querySnapshot);
      }, onError: (e) {
        log("Error listening to available orders: $e");
      });
    } catch (e) {
      log("Error fetching initial available orders: $e");
    }
  }

  void _handleAvailableOrdersUpdate(QuerySnapshot querySnapshot) {
    try {
      // Don't process orders if user is offline
      if (!widget.isOnline) {
        log("User is offline, not processing order updates");
        setState(() {
          if (!_pedidoAceito) {
            _clearMarkersExceptCurrentPosition();
          }
        });
        return;
      }

      setState(() {
        if (!_pedidoAceito) {
          _clearMarkersExceptCurrentPosition();
        }
      });

      // Agrupar pedidos por loja
      Map<String, List<OrderModel>> pedidosPorLoja = {};

      for (var doc in querySnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final order = OrderModel.fromJson(data);

        LatLng? lojaPos =
            _getLatLng(order.vendor.address_store?.location.geoPoint);

        if (lojaPos != null && !_pedidoAceito && _currentPosition != null) {
          double distanceInKm = _calculateDistance(_currentPosition!.latitude,
              _currentPosition!.longitude, lojaPos.latitude, lojaPos.longitude);

          if (distanceInKm <= maxStoreDistanceInKM) {
            // Usar ID da loja como chave para agrupar
            String lojaId = order.vendor.id ?? order.id;

            if (!pedidosPorLoja.containsKey(lojaId)) {
              pedidosPorLoja[lojaId] = [];
            }
            pedidosPorLoja[lojaId]!.add(order);
          } else {
            log("Store for order ${doc.id} filtered out: distance ${distanceInKm.toStringAsFixed(1)} km exceeds limit of $maxStoreDistanceInKM km");
          }
        }
      }

      // Criar marcadores para cada loja com a quantidade de pedidos
      pedidosPorLoja.forEach((lojaId, pedidos) {
        if (pedidos.isNotEmpty) {
          final primeiroPedido = pedidos.first;
          LatLng? lojaPos = _getLatLng(
              primeiroPedido.vendor.address_store?.location.geoPoint);

          if (lojaPos != null) {
            String titulo = pedidos.length == 1
                ? "Loja: ${primeiroPedido.vendor.title}"
                : "Loja: ${primeiroPedido.vendor.title} (${pedidos.length} pedidos)";

            setState(() {
              _markers['loja_$lojaId'] = Marker(
                markerId: MarkerId('loja_$lojaId'),
                position: lojaPos,
                icon: BitmapDescriptor.defaultMarkerWithHue(pedidos.length > 1
                    ? BitmapDescriptor.hueOrange
                    : BitmapDescriptor.hueGreen),
                infoWindow: InfoWindow(title: titulo, snippet: ""),
                onTap: () {
                  if (pedidos.length == 1) {
                    _selectOrderAndShowPanel(pedidos.first);
                  } else {
                    _showMultipleOrdersDialog(pedidos);
                  }
                },
              );
            });
          }
        }
      });
    } catch (e) {
      log("Error handling available orders update: $e");
    }
  }

  double _calculateDistance(
      double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371;
    double dLat = _degreesToRadians(lat2 - lat1);
    double dLon = _degreesToRadians(lon2 - lon1);

    double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) *
            math.cos(_degreesToRadians(lat2)) *
            math.sin(dLon / 2) *
            math.sin(dLon / 2);

    double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    double distance = earthRadius * c;

    return distance;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (math.pi / 180);
  }

  void fetchOrders() async {
    if (_currentPosition == null) return;

    // Only fetch orders if user is online
    if (!widget.isOnline) {
      log("User is offline, not fetching orders");
      setState(() {
        if (!_pedidoAceito) {
          _clearMarkersExceptCurrentPosition();
        }
      });
      return;
    }

    try {
      final returnOrderSnapshot = await FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: CURRENT_USER_ID)
          .where('status', isEqualTo: OrderStatus.delivered.description)
          .where('has_return', isEqualTo: true)
          .limit(1)
          .get();

      if (returnOrderSnapshot.docs.isNotEmpty) {
        final returnOrder =
            OrderModel.fromJson(returnOrderSnapshot.docs.first.data());

        log("Pedido de devolução encontrado no fetchOrders: ID=${returnOrder.id}");

        LatLng? storePos =
            _getLatLng(returnOrder.vendor.address_store?.location.geoPoint);

        if (_currentPosition != null && storePos != null) {
          final returnRoute =
              await getRouteCoordinates(_currentPosition!, storePos);

          setState(() {
            _selectedOrder = returnOrder;
            _pedidoAceito = true;
            _polylines.clear();
            _clearMarkersExceptCurrentPosition();

            _polylines.add(Polyline(
              polylineId: const PolylineId("rota_de_retorno"),
              points: returnRoute,
              color: Colors.red,
              width: 5,
            ));

            _markers['destino'] = Marker(
              markerId: const MarkerId('destino'),
              position: storePos,
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueGreen),
              infoWindow: InfoWindow(
                  title: "Devolução para: ${returnOrder.vendor.title}",
                  snippet: ""),
              onTap: () {
                if (_pedidoAceito && _selectedOrder != null) {
                  showReturnOrderBottomSheet(context, returnOrder, () {
                    setState(() {
                      _selectedOrder = null;
                      _pedidoAceito = false;
                    });
                    _checkForAcceptedOrder();
                  });
                }
              },
            );
          });

          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted) {
              showReturnOrderBottomSheet(context, returnOrder, () {
                setState(() {
                  _selectedOrder = null;
                  _pedidoAceito = false;
                });
                _checkForAcceptedOrder();
              });
            }
          });

          return;
        }
      }

      final existingOrderSnapshot = await FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: CURRENT_USER_ID)
          .where('status', whereIn: [
            OrderStatus.driverAccepted.description,
            OrderStatus.driverOnTheWay.description
          ])
          .limit(1)
          .get();

      if (existingOrderSnapshot.docs.isNotEmpty) {
        final existingOrder =
            OrderModel.fromJson(existingOrderSnapshot.docs.first.data());

        bool isDeliveryInProgress =
            existingOrder.status == OrderStatus.driverOnTheWay;

        LatLng? destinationPos;
        String destinationTitle;
        String destinationSnippet;
        String routeId;

        if (isDeliveryInProgress) {
          destinationPos = _getLatLng(existingOrder.author.shippingAddress
              ?.firstWhere((a) => a.isDefault == true,
                  orElse: () => existingOrder.author.shippingAddress!.first)
              .location
              ?.geoPoint);
          destinationTitle = "Loja: ${existingOrder.vendor.title}";
          destinationSnippet =
              "Entrega para ${existingOrder.author.firstName} ${existingOrder.author.lastName}";
          routeId = "rota_ate_cliente";
        } else {
          destinationPos =
              _getLatLng(existingOrder.vendor.address_store?.location.geoPoint);
          destinationTitle = "Loja: ${existingOrder.vendor.title}";
          destinationSnippet = "Coleta de pedido";
          routeId = "rota_ate_loja";
        }

        if (_currentPosition != null && destinationPos != null) {
          final rota =
              await getRouteCoordinates(_currentPosition!, destinationPos);
          _polylines.add(Polyline(
            polylineId: PolylineId(routeId),
            points: rota,
            color: Colors.orange,
            width: 5,
          ));

          _markers['destino'] = Marker(
            markerId: const MarkerId('destino'),
            position: destinationPos,
            icon: BitmapDescriptor.defaultMarkerWithHue(isDeliveryInProgress
                ? BitmapDescriptor.hueRed
                : BitmapDescriptor.hueGreen),
            infoWindow: InfoWindow(
                title: destinationTitle, snippet: destinationSnippet),
            onTap: () {
              if (_pedidoAceito && _selectedOrder != null) {
                showStoreInfoDialog(context, _selectedOrder!);
              } else {
                _showBottomSheet();
              }
            },
          );
        }

        setState(() {
          _selectedOrder = existingOrder;
          _pedidoAceito = true;
        });
      } else {
        final snapshot = await FirebaseFirestore.instance
            .collection(ORDERS)
            .where('status', isEqualTo: OrderStatus.driverSearching.description)
            .get();

        // Agrupar pedidos por loja
        Map<String, List<OrderModel>> pedidosPorLoja = {};

        for (var doc in snapshot.docs) {
          final data = doc.data();
          final order = OrderModel.fromJson(data);

          LatLng? lojaPos =
              _getLatLng(order.vendor.address_store?.location.geoPoint);

          if (lojaPos != null && _currentPosition != null) {
            double distanceInKm = _calculateDistance(
                _currentPosition!.latitude,
                _currentPosition!.longitude,
                lojaPos.latitude,
                lojaPos.longitude);

            if (distanceInKm <= maxStoreDistanceInKM) {
              String lojaId = order.vendor.id ?? order.id;

              if (!pedidosPorLoja.containsKey(lojaId)) {
                pedidosPorLoja[lojaId] = [];
              }
              pedidosPorLoja[lojaId]!.add(order);
            } else {
              log("Store for order ${doc.id} filtered out: distance ${distanceInKm.toStringAsFixed(1)} km exceeds limit of $maxStoreDistanceInKM km");
            }
          }
        }

        // Criar marcadores para cada loja
        pedidosPorLoja.forEach((lojaId, pedidos) {
          if (pedidos.isNotEmpty) {
            final primeiroPedido = pedidos.first;
            LatLng? lojaPos = _getLatLng(
                primeiroPedido.vendor.address_store?.location.geoPoint);

            if (lojaPos != null) {
              String titulo = pedidos.length == 1
                  ? "Loja: ${primeiroPedido.vendor.title}"
                  : "Loja: ${primeiroPedido.vendor.title} (${pedidos.length} pedidos)";

              _markers['loja_$lojaId'] = Marker(
                markerId: MarkerId('loja_$lojaId'),
                position: lojaPos,
                icon: BitmapDescriptor.defaultMarkerWithHue(pedidos.length > 1
                    ? BitmapDescriptor.hueOrange
                    : BitmapDescriptor.hueGreen),
                infoWindow: InfoWindow(title: titulo, snippet: ""),
                onTap: () {
                  if (pedidos.length == 1) {
                    _selectOrderAndShowPanel(pedidos.first);
                  } else {
                    _showMultipleOrdersDialog(pedidos);
                  }
                },
              );
            }
          }
        });

        setState(() {});
      }
    } catch (e) {
      log("Erro ao buscar pedidos: $e");
    }
  }

  LatLng? _getLatLng(GeoPoint? location) {
    if (location == null) return null;
    return LatLng(location.latitude, location.longitude);
  }

  Future _selectOrderAndShowPanel(OrderModel order) async {
    final loja = _getLatLng(order.vendor.address_store?.location.geoPoint);
    final cliente = _getLatLng(order.author.shippingAddress
        ?.firstWhere((a) => a.isDefault == true)
        .location
        ?.geoPoint);

    if (_currentPosition == null || loja == null || cliente == null) return;

    double storeDistanceInKm = _calculateDistance(_currentPosition!.latitude,
        _currentPosition!.longitude, loja.latitude, loja.longitude);

    log("Selected order from store at distance: ${storeDistanceInKm.toStringAsFixed(1)} km");

    setState(() {
      _selectedOrder = order;
      _selectedRouteInfo = null;
      _polylines.clear();
      _clearMarkersExceptCurrentPosition();
      _pedidoAceito = false;
    });

    // Calculate only the distance from store to client
    final routeInfo = await getRouteInfo(loja, cliente);

    final totalDistance = routeInfo.distance;
    final totalDuration = routeInfo.duration;

    final rotaParaLoja = await getRouteCoordinates(_currentPosition!, loja);

    setState(() {
      _selectedRouteInfo = RouteInfo(
        route: [],
        distance: totalDistance,
        duration: totalDuration,
      );

      _polylines.add(Polyline(
        polylineId: const PolylineId("rota_para_loja"),
        points: rotaParaLoja,
        color: Colors.orange,
        width: 5,
      ));

      bool isDeliveryInProgress = order.status == OrderStatus.driverOnTheWay;

      LatLng? destinationPos;
      String destinationTitle;
      String destinationSnippet;

      if (isDeliveryInProgress) {
        destinationPos = cliente;
        destinationTitle =
            "Entrega para: ${order.author.firstName} ${order.author.lastName}";
        destinationSnippet = "";
      } else {
        destinationPos = loja;
        destinationTitle = "Loja: ${order.vendor.title}";
        destinationSnippet = "";
      }

      _markers['destino'] = Marker(
        markerId: const MarkerId('destino'),
        position: destinationPos,
        icon: BitmapDescriptor.defaultMarkerWithHue(isDeliveryInProgress
            ? BitmapDescriptor.hueRed
            : BitmapDescriptor.hueGreen),
        infoWindow:
            InfoWindow(title: destinationTitle, snippet: destinationSnippet),
        onTap: () {
          if (_pedidoAceito && _selectedOrder != null) {
            showStoreInfoDialog(context, _selectedOrder!);
          } else {
            _showBottomSheet();
          }
        },
      );
    });

    _showBottomSheet();
  }

  void _forceMapReload() {
    log("Forcing map reload");
    if (mounted) {
      setState(() {
        _markers.clear();
        _polylines.clear();
      });

      _determinePosition();
      fetchOrders();

      if (_currentPosition != null) {
        _updateCurrentPositionMarker();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Stack(
              children: [
                GoogleMap(
                  mapType: MapType.terrain,
                  onMapCreated: _onMapCreated,
                  myLocationEnabled: false,
                  myLocationButtonEnabled: false,
                  zoomControlsEnabled: false,
                  markers: _markers.values.toSet(),
                  polylines: _polylines,
                  initialCameraPosition: CameraPosition(
                    target: _currentPosition ?? const LatLng(0, 0),
                    zoom: 17,
                  ),
                  onTap: _onMapTap,
                ),
                const Positioned(
                  right: 5,
                  top: 6,
                  child: Column(
                    children: [
                      FloatingActionButton(
                        heroTag: 'zoomInButton',
                        mini: false,
                        backgroundColor: Colors.white,
                        onPressed: _zoomIn,
                        child: Icon(
                          Icons.zoom_in_outlined,
                          color: Color(0xff425799),
                        ),
                      ),
                      SizedBox(height: 16),
                      FloatingActionButton(
                        heroTag: "zoomOutButton",
                        mini: false,
                        backgroundColor: Colors.white,
                        onPressed: _zoomOut,
                        child: Icon(
                          Icons.zoom_out_outlined,
                          color: Color(0xff425799),
                        ),
                      ),
                      SizedBox(height: 16),
                      FloatingActionButton(
                        heroTag: "centerLocationButton",
                        mini: false,
                        backgroundColor: Colors.white,
                        onPressed: _centerOnCurrentLocation,
                        child: Icon(
                          Icons.my_location,
                          color: Color(0xff425799),
                        ),
                      ),
                      SizedBox(height: 16),
                      FloatingActionButton(
                        heroTag: "reloadMapButton",
                        mini: false,
                        backgroundColor: Colors.white,
                        onPressed: _forceMapReload,
                        child: Icon(
                          Icons.refresh,
                          color: Color(0xff425799),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  void _onMapCreated(GoogleMapController controller) {
    try {
      _mapController = controller;
      if (_currentPosition != null) {
        controller.animateCamera(
          CameraUpdate.newLatLngZoom(_currentPosition!, 15),
        );

        _updateCurrentPositionMarker();
      } else {
        log("Current position is null in _onMapCreated, trying to determine position again");
        _determinePosition();
      }
      log("Map controller created successfully");
    } catch (e) {
      log("Error in _onMapCreated: $e");
    }
  }

  void _centerOnCurrentLocation() {
    if (_currentPosition == null) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Aguardando localização...'),
            duration: Duration(seconds: 2),
          ),
        );
      }
      _determinePosition();
      return;
    }

    try {
      _mapController.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: _currentPosition!,
            zoom: 17.0,
            tilt: 0,
          ),
        ),
      );

      _updateCurrentPositionMarker();
    } catch (e) {
      log("Erro ao centralizar mapa: $e");
    }
  }

  void _zoomIn() {
    try {
      _mapController.getZoomLevel().then((currentZoom) {
        double newZoom = currentZoom + 1.0;

        newZoom = newZoom > 20.0 ? 20.0 : newZoom;

        _mapController.animateCamera(
          CameraUpdate.zoomTo(newZoom),
        );
      });
    } catch (e) {
      log("Erro ao aumentar zoom: $e");
    }
  }

  void _zoomOut() {
    try {
      _mapController.getZoomLevel().then((currentZoom) {
        double newZoom = currentZoom - 1.0;

        newZoom = newZoom < 2.0 ? 2.0 : newZoom;

        _mapController.animateCamera(
          CameraUpdate.zoomTo(newZoom),
        );
      });
    } catch (e) {
      log("Erro ao reduzir zoom: $e");
    }
  }

  double _currentHeading = 0.0;

  void _updateCurrentPositionMarker() {
    if (_currentPosition != null) {
      setState(() {
        _markers['current_position'] = Marker(
          markerId: const MarkerId('current_position'),
          position: _currentPosition!,
          icon: _deliveryPersonIcon ?? BitmapDescriptor.defaultMarker,
          infoWindow: const InfoWindow(title: "Minha Localização"),
          zIndex: 2,
          rotation: _currentHeading,
          anchor: const Offset(0.5, 0.5),
        );
      });
    }
  }

  void _clearMarkersExceptCurrentPosition() {
    final currentPosMarker = _markers['current_position'];
    _markers.clear();
    if (currentPosMarker != null) {
      _markers['current_position'] = currentPosMarker;
    } else if (_currentPosition != null) {
      _updateCurrentPositionMarker();
    }
  }

  void _startPositionUpdates() {
    _positionStreamSubscription?.cancel();

    const locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10,
    );

    _positionStreamSubscription =
        Geolocator.getPositionStream(locationSettings: locationSettings)
            .listen((Position position) {
      setState(() {
        _currentPosition = LatLng(position.latitude, position.longitude);

        _currentHeading = position.heading;

        _updateCurrentPositionMarker();

        _orderMonitor.updatePosition(_currentPosition!);
      });
    });
  }

  void _onMapTap(LatLng position) {
    if (_selectedOrder != null && !_pedidoAceito) {
      setState(() {
        _selectedOrder = null;
        _selectedRouteInfo = null;
        _polylines.clear();
        _clearMarkersExceptCurrentPosition();
        fetchOrders();
      });
    }
  }

  void _showBottomSheet() {
    if (_selectedOrder == null || _selectedRouteInfo == null) return;

    showOrderBottomSheet(
      context: context,
      pedidoAceito: _pedidoAceito,
      order: _selectedOrder!,
      distanceInKm: (_selectedRouteInfo!.distance) / 1000,
      durationInMinutes: (_selectedRouteInfo!.duration) / 60,
      onAccept: () async {
        try {
          final docRef = FirebaseFirestore.instance
              .collection(ORDERS)
              .doc(_selectedOrder!.id);

          await docRef.update({
            "entregador_id": CURRENT_USER_ID,
            "horaAceite": Timestamp.now(),
            "status": OrderStatus.driverAccepted.description,
          });

          // try {
          //   final apiResult = await TaEntregueApiService.acceptOrder(
          //     idRemoto: _selectedOrder!.id,
          //     idCliente: int.tryParse(_selectedOrder!.authorID) ?? 0,
          //     idEntregador: int.tryParse(CURRENT_USER_ID) ?? 0,
          //     valor: _selectedOrder!.price ?? "0,00",
          //   );

          //   if (!apiResult['success']) {
          //     log("Aviso: API externa retornou erro: ${apiResult['mensagem']}");
          //   } else {
          //     log("Pedido aceito na API externa com sucesso");
          //   }
          // } catch (apiError) {
          //   log("Erro ao chamar API externa: $apiError");
          // }

          final updatedDoc = await docRef.get();
          final updatedOrder = OrderModel.fromJson(updatedDoc.data()!);

          setState(() {
            _selectedOrder = updatedOrder;
            _pedidoAceito = true;
          });

          _listenToAcceptedOrder(_selectedOrder!.id);

          Future.delayed(const Duration(seconds: 1), () {
            presentOrderAcceptedDialog(context, updatedOrder);

            Future.delayed(const Duration(milliseconds: 500), () {
              showStoreInfoDialog(context, updatedOrder);
            });
          });
        } catch (e) {
          log("Erro ao aceitar pedido: $e");
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text("Erro ao aceitar o pedido.")),
          );
        }
      },
      onDecline: () {
        setState(() {
          _selectedOrder = null;
          _selectedRouteInfo = null;
          _polylines.clear();
          _clearMarkersExceptCurrentPosition();
        });
      },
    );
  }

  void _showMultipleOrdersDialog(List<OrderModel> pedidos) {
    showModalBottomSheet(
      enableDrag: true,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.70,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(height: 20),
                  GestureDetector(
                    onTap: () {},
                    child: Container(
                      width: 50,
                      height: 5,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  )
                ],
              ),
              const SizedBox(height: 20),
              Text(
                'Loja: ${pedidos.first.vendor.title}',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xff425799),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),
              Container(
                padding: const EdgeInsets.all(10),
                decoration: const BoxDecoration(
                  color: Color.fromARGB(39, 201, 212, 247),
                ),
                width: MediaQuery.of(context).size.width,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.asset('assets/images/caixa.png',
                        height: 30, width: 30),
                    const SizedBox(width: 8),
                    Text(
                      "${pedidos.length} pedidos disponíveis",
                      style: const TextStyle(
                          fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 15),
              Expanded(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: pedidos.length,
                  itemBuilder: (context, index) {
                    final pedido = pedidos[index];
                    LatLng? lojaPos = _getLatLng(
                        pedido.vendor.address_store?.location.geoPoint);
                    double distanciaLoja = 0;

                    if (_currentPosition != null && lojaPos != null) {
                      distanciaLoja = _calculateDistance(
                          _currentPosition!.latitude,
                          _currentPosition!.longitude,
                          lojaPos.latitude,
                          lojaPos.longitude);
                    }

                    return Card(
                      margin: const EdgeInsets.symmetric(
                          vertical: 8, horizontal: 4),
                      elevation: 3,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: const BorderSide(
                            color: Color(0xff425799), width: 1),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Pedido #${pedido.id.substring(0, 8)}...',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                    color: Color(0xff425799),
                                  ),
                                ),
                                Text(
                                  'R\$ ${pedido.price ?? "0,00"}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                    color: Colors.green,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                const Icon(Icons.person,
                                    size: 16, color: Colors.grey),
                                const SizedBox(width: 4),
                                Expanded(
                                  child: Text(
                                    'Cliente: ${pedido.author.firstName} ${pedido.author.lastName}',
                                    style: const TextStyle(fontSize: 14),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                const Icon(Icons.location_on,
                                    size: 16, color: Colors.redAccent),
                                const SizedBox(width: 4),
                                Expanded(
                                  child: Text(
                                    'Endereço: ${pedido.author.shippingAddress?.first.bairro ?? ""}',
                                    style: const TextStyle(fontSize: 14),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                const Icon(Icons.location_searching,
                                    size: 16, color: Colors.blueAccent),
                                const SizedBox(width: 4),
                                Text(
                                  'Distância até loja: ${distanciaLoja.toStringAsFixed(1)} km',
                                  style: const TextStyle(fontSize: 14),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xff425799),
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                onPressed: () {
                                  Navigator.pop(context);
                                  _selectOrderAndShowPanel(pedido);
                                },
                                child: const Text('Selecionar Pedido'),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 10),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey,
                  foregroundColor: Colors.white,
                  minimumSize: const Size(double.infinity, 45),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                onPressed: () => Navigator.pop(context),
                child: const Text(
                  'Cancelar',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

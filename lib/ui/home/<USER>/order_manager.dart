import 'dart:async';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/services/OrderMonitor.dart';
import 'package:emartdriver/ui/home/<USER>/distance_calculator.dart';
import 'package:emartdriver/ui/home/<USER>/marker_utils.dart';
import 'package:emartdriver/vendor_status_enum.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class OrderManager {
  final String currentUserId = FirebaseAuth.instance.currentUser?.uid ?? '';
  final OrderMonitor _orderMonitor = OrderMonitor();

  StreamSubscription<QuerySnapshot>? _ordersSubscription;
  StreamSubscription<DocumentSnapshot>? _acceptedOrderSubscription;

  OrderModel? _selectedOrder;
  OrderModel? _newOrder;
  bool _showingNewOrderNotification = false;

  // Callbacks
  Function(OrderModel)? onNewOrderReceived;
  Function(OrderModel)? onOrderAccepted;
  Function(OrderModel)? onOrderStatusChanged;
  Function(List<OrderModel>)? onAvailableOrdersUpdated;
  Function()? onOrdersCleared;

  OrderModel? get selectedOrder => _selectedOrder;
  OrderModel? get newOrder => _newOrder;
  bool get showingNewOrderNotification => _showingNewOrderNotification;

  /// Initialize order manager
  void initialize({
    Function(OrderModel)? onNewOrderReceived,
    Function(OrderModel)? onOrderAccepted,
    Function(OrderModel)? onOrderStatusChanged,
    Function(List<OrderModel>)? onAvailableOrdersUpdated,
    Function()? onOrdersCleared,
  }) {
    this.onNewOrderReceived = onNewOrderReceived;
    this.onOrderAccepted = onOrderAccepted;
    this.onOrderStatusChanged = onOrderStatusChanged;
    this.onAvailableOrdersUpdated = onAvailableOrdersUpdated;
    this.onOrdersCleared = onOrdersCleared;
  }

  /// Initialize order monitor
  void initializeOrderMonitor(LatLng? currentPosition, bool isOnline) {
    if (currentPosition != null && isOnline) {
      _orderMonitor.initialize(
        onNewOrder: _handleNewOrder,
        currentPosition: currentPosition,
        currentUserId: currentUserId,
      );
    }
  }

  /// Handle online status change
  void handleOnlineStatusChange(bool isOnline, LatLng? currentPosition) {
    log("Online status changed to: $isOnline");

    if (isOnline) {
      // User went online - start listening to orders
      if (currentPosition != null) {
        initializeOrderMonitor(currentPosition, isOnline);
        startFirebaseListeners();
      }
    } else {
      // User went offline - stop listening and clear orders
      _orderMonitor.dispose();
      _ordersSubscription?.cancel();
      _acceptedOrderSubscription?.cancel();
      onOrdersCleared?.call();
    }
  }

  /// Handle new order
  void _handleNewOrder(OrderModel order) {
    if (order.status == OrderStatus.driverAccepted.description &&
        order.entregador_id != null &&
        order.entregador_id!.isNotEmpty &&
        order.entregador_id != currentUserId) {
      log("Order ${order.id} already accepted by another driver: ${order.entregador_id}");
      return;
    }

    if (!_showingNewOrderNotification) {
      _newOrder = order;
      _showingNewOrderNotification = true;
      onNewOrderReceived?.call(order);
    }
  }

  /// Accept new order
  Future<bool> acceptNewOrder(OrderModel order) async {
    try {
      final docRef =
          FirebaseFirestore.instance.collection(ORDERS).doc(order.id);

      await docRef.update({
        "entregador_id": currentUserId,
        "horaAceite": Timestamp.now(),
        "status": OrderStatus.driverAccepted.description,
      });

      final updatedDoc = await docRef.get();
      final updatedOrder = OrderModel.fromJson(updatedDoc.data()!);

      _selectedOrder = updatedOrder;
      _newOrder = null;
      _showingNewOrderNotification = false;

      onOrderAccepted?.call(updatedOrder);
      _listenToAcceptedOrder(_selectedOrder!.id);

      return true;
    } catch (e) {
      log("Error accepting order: $e");
      return false;
    }
  }

  /// Decline new order
  void declineNewOrder() {
    _newOrder = null;
    _showingNewOrderNotification = false;
  }

  /// Start Firebase listeners
  void startFirebaseListeners() {
    _ordersSubscription?.cancel();
    _acceptedOrderSubscription?.cancel();
    _checkForAcceptedOrder();
  }

  /// Check for accepted order
  void _checkForAcceptedOrder() async {
    try {
      // Check for return orders first
      final returnQuery = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('status', isEqualTo: OrderStatus.delivered.description)
          .where('has_return', isEqualTo: true)
          .limit(1);

      final returnSnapshot = await returnQuery.get();

      if (returnSnapshot.docs.isNotEmpty) {
        final orderId = returnSnapshot.docs.first.id;
        log("Return order found with ID: $orderId");
        _listenToReturnOrder(orderId);
        return;
      }

      // Check for accepted orders
      final query = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('status', whereIn: [
        OrderStatus.driverAccepted.description,
        OrderStatus.driverOnTheWay.description,
        OrderStatus.driverPending.description
      ]).limit(1);

      final snapshot = await query.get();

      if (snapshot.docs.isNotEmpty) {
        final orderId = snapshot.docs.first.id;
        _listenToAcceptedOrder(orderId);
      } else {
        _listenToAvailableOrders();
      }
    } catch (e) {
      log("Error checking for accepted orders: $e");
    }
  }

  /// Listen to return order
  void _listenToReturnOrder(String orderId) async {
    _ordersSubscription?.cancel();

    try {
      final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(orderId);
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        _handleReturnOrderUpdate(docSnapshot);

        _acceptedOrderSubscription = docRef.snapshots().listen((docSnapshot) {
          if (docSnapshot.exists) {
            _handleReturnOrderUpdate(docSnapshot);
          } else {
            _listenToAvailableOrders();
          }
        }, onError: (e) {
          log("Error listening to return order: $e");
        });
      } else {
        _listenToAvailableOrders();
      }
    } catch (e) {
      log("Error fetching initial return order: $e");
      _listenToAvailableOrders();
    }
  }

  /// Handle return order update
  void _handleReturnOrderUpdate(DocumentSnapshot docSnapshot) async {
    try {
      log("Processing return order...");
      final returnOrder =
          OrderModel.fromJson(docSnapshot.data() as Map<String, dynamic>);

      if (returnOrder.status != OrderStatus.delivered ||
          !returnOrder.has_return) {
        log("Order is no longer in 'delivered' status or doesn't have has_return=true");
        _checkForAcceptedOrder();
        return;
      }

      _selectedOrder = returnOrder;
      onOrderStatusChanged?.call(returnOrder);
    } catch (e) {
      log("Error handling return order update: $e");
    }
  }

  /// Listen to accepted order
  void _listenToAcceptedOrder(String orderId) async {
    _ordersSubscription?.cancel();

    try {
      final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(orderId);
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        _handleAcceptedOrderUpdate(docSnapshot);

        _acceptedOrderSubscription = docRef.snapshots().listen((docSnapshot) {
          if (docSnapshot.exists) {
            _handleAcceptedOrderUpdate(docSnapshot);
          } else {
            _listenToAvailableOrders();
          }
        }, onError: (e) {
          log("Error listening to accepted order: $e");
        });
      } else {
        _listenToAvailableOrders();
      }
    } catch (e) {
      log("Error fetching initial accepted order: $e");
      _listenToAvailableOrders();
    }
  }

  /// Handle accepted order update
  void _handleAcceptedOrderUpdate(DocumentSnapshot docSnapshot) async {
    try {
      final existingOrder =
          OrderModel.fromJson(docSnapshot.data() as Map<String, dynamic>);

      _selectedOrder = existingOrder;
      onOrderStatusChanged?.call(existingOrder);
    } catch (e) {
      log("Error handling accepted order update: $e");
    }
  }

  /// Listen to available orders
  void _listenToAvailableOrders() async {
    _acceptedOrderSubscription?.cancel();

    try {
      final query = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('status', isEqualTo: OrderStatus.driverSearching.description);

      final snapshot = await query.get();
      _handleAvailableOrdersUpdate(snapshot);

      _ordersSubscription = query.snapshots().listen((querySnapshot) {
        _handleAvailableOrdersUpdate(querySnapshot);
      }, onError: (e) {
        log("Error listening to available orders: $e");
      });
    } catch (e) {
      log("Error fetching initial available orders: $e");
    }
  }

  /// Handle available orders update
  void _handleAvailableOrdersUpdate(QuerySnapshot querySnapshot) {
    try {
      List<OrderModel> availableOrders = [];

      for (var doc in querySnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final order = OrderModel.fromJson(data);
        availableOrders.add(order);
      }

      onAvailableOrdersUpdated?.call(availableOrders);
    } catch (e) {
      log("Error handling available orders update: $e");
    }
  }

  /// Fetch orders
  Future<List<OrderModel>> fetchOrders(LatLng? currentPosition) async {
    if (currentPosition == null) return [];

    try {
      // Check for return orders first
      final returnOrderSnapshot = await FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('status', isEqualTo: OrderStatus.delivered.description)
          .where('has_return', isEqualTo: true)
          .limit(1)
          .get();

      if (returnOrderSnapshot.docs.isNotEmpty) {
        final returnOrder =
            OrderModel.fromJson(returnOrderSnapshot.docs.first.data());
        log("Return order found in fetchOrders: ID=${returnOrder.id}");
        return [returnOrder];
      }

      // Check for existing accepted orders
      final existingOrderSnapshot = await FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('status', whereIn: [
            OrderStatus.driverAccepted.description,
            OrderStatus.driverOnTheWay.description
          ])
          .limit(1)
          .get();

      if (existingOrderSnapshot.docs.isNotEmpty) {
        final existingOrder =
            OrderModel.fromJson(existingOrderSnapshot.docs.first.data());
        return [existingOrder];
      }

      // Fetch available orders
      final snapshot = await FirebaseFirestore.instance
          .collection(ORDERS)
          .where('status', isEqualTo: OrderStatus.driverSearching.description)
          .get();

      List<OrderModel> orders = [];
      for (var doc in snapshot.docs) {
        final data = doc.data();
        final order = OrderModel.fromJson(data);

        LatLng? storePos = MarkerUtils.getLatLng(
            order.vendor.address_store?.location.geoPoint);

        if (storePos != null) {
          double distanceInKm = DistanceCalculator.calculateDistance(
              currentPosition.latitude,
              currentPosition.longitude,
              storePos.latitude,
              storePos.longitude);

          if (distanceInKm <= maxStoreDistanceInKM) {
            orders.add(order);
          } else {
            log("Store for order ${doc.id} filtered out: distance ${distanceInKm.toStringAsFixed(1)} km exceeds limit of $maxStoreDistanceInKM km");
          }
        }
      }

      return orders;
    } catch (e) {
      log("Error fetching orders: $e");
      return [];
    }
  }

  /// Update position in order monitor
  void updatePosition(LatLng position) {
    _orderMonitor.updatePosition(position);
  }

  /// Dispose resources
  void dispose() {
    _ordersSubscription?.cancel();
    _acceptedOrderSubscription?.cancel();
    _orderMonitor.dispose();
  }
}
